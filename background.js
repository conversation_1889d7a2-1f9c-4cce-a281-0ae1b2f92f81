let isPopupOpen;
const targetBaseUrl = "https://champlain.crm.dynamics.com/main.aspx";

// Retrieve the isPopupOpen state from local storage when the extension starts
chrome.storage.local.get('isPopupOpen', (data) => {
  console.log(isPopupOpen);
  if (data.isPopupOpen !== undefined) {
    isPopupOpen = data.isPopupOpen;
  } else {
    // If not found in storage, set default value
    isPopupOpen = false;
  }
});

chrome.action.onClicked.addListener((tab) => {
  if (isPopupOpen) {
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['removePopup.js']
    });
    isPopupOpen = false;
  }
  else {
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['content.js']
    });
    isPopupOpen = true;
  }

  // Store the updated isPopupOpen state in local storage
  chrome.storage.local.set({ isPopupOpen });

  console.log(isPopupOpen);
});

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete') {
    if (tab.url.startsWith(targetBaseUrl)) {
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      });
      isPopupOpen = true;
    } else {
      if (isPopupOpen) {
        chrome.scripting.executeScript({
          target: { tabId: tabId },
          files: ['removePopup.js']
        });
        isPopupOpen = false;
      }
    }
    // Store the updated isPopupOpen state in local storage
    chrome.storage.local.set({ isPopupOpen });
  }
});

chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
  if (isPopupOpen) {
    chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: ['removePopup.js']
    });
    isPopupOpen = false;
    // Store the updated isPopupOpen state in local storage
    chrome.storage.local.set({ isPopupOpen });
  }
});

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === "closePopup") {
    isPopupOpen = false;
    // Store the updated isPopupOpen state in local storage
    chrome.storage.local.set({ isPopupOpen });
  }
});
