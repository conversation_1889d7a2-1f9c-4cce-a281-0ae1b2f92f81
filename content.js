(function() {
  // Function to create and display the popup
  // http://localhost:5000/careplan-on-extension

  if (document.getElementById('extension-popup')) {
      console.log('Popup already exists.');
      return;
  }

  function createPopup() {
    console.log('Creating popup...');

    // Create the popup div
    const popup = document.createElement('div');
    popup.id = 'extension-popup';
    popup.style.position = 'fixed';
    popup.style.top = '0px';
    popup.style.right = '0';
    popup.style.width = '500px';
    popup.style.height = '100vh';
    popup.style.backgroundColor = '#fff';
    popup.style.zIndex = '1000';
    popup.style.transition = "right 0.3s";
    popup.style.overflow = "visible";
    popup.innerHTML = `
        <button id="close-popup" style="cursor: pointer;width: 38px;height: 38px;position: absolute;top: 6px;left: -19px;box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.2);border-radius: 50%;background-color: #fff;border:0;">
          <img src="https://websitestaginbucket.storage.googleapis.com/wp-content/uploads/2024/08/02161037/close.png" style="width: 100%;height: auto;">
        </button>
        <iframe id="popup-iframe" style="width:100%;height: 100%;box-shadow: -2px 0px 10px 0px rgba(0,0,0,0.25);" src="http://127.0.0.1:5500/index.html" frameborder="0"></iframe>
    `;

    // Append the popup to the body
    document.body.appendChild(popup);
    console.log('Popup added to the body.');

    // Add event listener to the close button
    document.getElementById('close-popup').addEventListener('click', function(e) {
      e.preventDefault();
      popup.style.right = '-500px';
      document.getElementById('close-popup').style.display = "none";
      document.getElementById('open-popup').style.display = "flex";
    });

    function findCompanyName() {
      // This detects patient account number from the user opened tab of EMR
      // and sends the value for patient account number to the iframe app.
      const iframe = document.getElementById('popup-iframe');
      let companyName = null;
    
      iframe.addEventListener('load', function () {
        const intervalId = setInterval(function () {
          let uri_curr = document.URL;
          
          if (uri_curr.includes('champlain')) {
            // // Get all contact rows from the table
            // const contactRows = document.querySelectorAll("#dataSetRoot_ContactSubgrid .ag-row");

            // console.log(contactRows);

            // // Array to store contact objects
            // const contacts = [];

            // contactRows.forEach(row => {
            //   // Get contact data using col-id attributes
            //   const nameDiv = row.querySelector('[col-id="fullname"]');
            //   const phoneDiv = row.querySelector('[col-id="chm_workphone"]');
            //   const mobileDiv = row.querySelector('[col-id="chm_personalmobile"]');
            //   const emailDiv = row.querySelector('[col-id="emailaddress1"]');

            //   console.log('Found divs:', { nameDiv, phoneDiv, mobileDiv, emailDiv });

            //   // Extract text from the first span in each div
            //   const contact = {
            //     name: nameDiv ? (nameDiv.querySelector('span')?.textContent.trim() || '') : '',
            //     phone: phoneDiv ? (phoneDiv.querySelector('span')?.textContent.trim() || '') : '',
            //     mobile: mobileDiv ? (mobileDiv.querySelector('span')?.textContent.trim() || '') : '',
            //     email: emailDiv ? (emailDiv.querySelector('span')?.textContent.trim() || '') : ''
            //   };

            //   // Only add contact if at least name is present
            //   contacts.push(contact);
            // });

            // console.log('Extracted contacts:', contacts);
            // clearInterval(intervalId);

            // Only to work on urls like nysucc, which is our EMR
            try {
              // Access the specific iframe that contains the input
              const targetIframe = document.getElementById("WebResource_AccountOfficeProfile");

              if (targetIframe) {
                // Try to access the iframe content (same-origin)
                const iframeDocument = targetIframe.contentDocument || targetIframe.contentWindow.document;
                const nameInput = iframeDocument.getElementById("plus_name");
                const rankInput = iframeDocument.getElementById("plus_rank");
                const typeInput = iframeDocument.getElementById("plus_customertypecode");
                const subTypeInput = iframeDocument.getElementById("plus_subtype");
                const directInput = iframeDocument.getElementById("plus_direct");
                const fundInput = iframeDocument.getElementById("plus_fund");
                const aumInput = iframeDocument.getElementById("plus_aum");
                const commentInput = iframeDocument.getElementById("plus_comment");
                const phoneInput = iframeDocument.getElementById("plus_mainphone");
                const plus_address1Input = iframeDocument.getElementById("plus_address1");
                const plus_address2Input = iframeDocument.getElementById("plus_address2");
                const crossInput = iframeDocument.getElementById("plus_crossstreet");
                const cityInput = iframeDocument.getElementById("plus_city");
                const stateInput = iframeDocument.getElementById("plus_state");
                const postalCodeInput = iframeDocument.getElementById("plus_postalcode");
                const countryInput = iframeDocument.getElementById("plus_country");
                const regionInput = iframeDocument.getElementById("plus_region");
                const websiteInput = iframeDocument.getElementById("plus_website");
                const managerElement = iframeDocument.querySelector('[data-id="chm_manager.fieldControl-LookupResultsDropdown_chm_manager_selected_tag_text"]');

                console.log('Target iframe found:', targetIframe);
                console.log('Name input:', nameInput);

                if (nameInput || contactRows) {
                  const companyNameFound = nameInput.value;
                  const rank = rankInput.options[rankInput.selectedIndex].text;
                  const type = typeInput.options[rankInput.selectedIndex].text;
                  const subType = subTypeInput.options[rankInput.selectedIndex].text;
                  const direct = directInput.value;
                  const fund = fundInput.value;
                  const aum = aumInput.value;
                  const comment = commentInput.value;
                  const phone = phoneInput.value;
                  const plus_address1 = plus_address1Input.value;
                  const plus_address2 = plus_address2Input.value;
                  const cross = crossInput.value;
                  const city = cityInput.value;
                  const state = stateInput.options[rankInput.selectedIndex].text;
                  const postalCode = postalCodeInput.value;
                  const country = countryInput.value;
                  const region = regionInput.options[rankInput.selectedIndex].text;
                  const website = websiteInput.value;
                  const consultant = managerElement ? managerElement.innerText : null;

                  if (companyNameFound) {
                    companyName = companyNameFound;
                    console.log('Sending to popup iframe:', iframe.contentWindow);
                    if (iframe.contentWindow) {
                      iframe.contentWindow.postMessage({ companyName, rank, type, subType,
                        direct, fund, aum, comment, phone, plus_address1, plus_address2,
                        cross, city, state, postalCode, country, region, website, consultant }, '*');
                    }
                    clearInterval(intervalId);
                  }
                }
              } else {
                console.log('Target iframe "WebResource_AccountOfficeProfile" not found');
              }
            } catch (error) {
              console.log('Cross-origin iframe detected or access error:', error);
              // For cross-origin iframes, we might need alternative approaches
            }
          } else {
            console.log(`>> interacting with non-ecw pages << no acct no will be sent to app`);
          }
        }, 500); // Check every 500ms
      });
    }

    findCompanyName();
  }

  // Wait for the DOM to be fully loaded before executing
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    createPopup();
  } else {
    document.addEventListener('DOMContentLoaded', createPopup);
  }
})();
